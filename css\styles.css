/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #8B5CF6;
    --secondary-color: #EC4899;
    --accent-color: #F59E0B;
    --background: #FFFFFF;
    --surface: #F9FAFB;
    --text-primary: #111827;
    --text-secondary: #6B7280;
    --border-color: #E5E7EB;
    --success-color: #10B981;
    --error-color: #EF4444;
    --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] {
    --background: #111827;
    --surface: #1F2937;
    --text-primary: #F9FAFB;
    --text-secondary: #D1D5DB;
    --border-color: #374151;
}

body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--background);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color 0.3s, color 0.3s;
}

/* Header Styles */
header {
    background-color: var(--surface);
    border-bottom: 1px solid var(--border-color);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: all 0.3s;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-links {
    display: flex;
    gap: 2rem;
    list-style: none;
}

.nav-links a {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-links a:hover {
    color: var(--primary-color);
}

.header-icons {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.icon-btn {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1.25rem;
    cursor: pointer;
    position: relative;
    transition: color 0.3s;
}

.icon-btn:hover {
    color: var(--primary-color);
}

.badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--accent-color);
    color: white;
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 9999px;
    font-weight: bold;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 8rem 2rem 4rem;
    text-align: center;
    margin-top: 70px;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    animation: fadeInUp 0.8s;
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: fadeInUp 0.8s 0.2s both;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    animation: fadeInUp 0.8s 0.4s both;
}

.btn {
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background-color: white;
    color: var(--primary-color);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background-color: white;
    color: var(--primary-color);
}

/* Products Section */
.products-section {
    padding: 4rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: var(--text-primary);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
}

.product-card {
    background-color: var(--surface);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.3s;
    cursor: pointer;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.product-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    background-color: var(--border-color);
}

.product-info {
    padding: 1.5rem;
}

.product-name {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.product-price {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.product-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.add-to-cart {
    flex: 1;
    background-color: var(--primary-color);
    color: white;
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
}

.add-to-cart:hover {
    background-color: var(--secondary-color);
}

.favorite-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s;
}

.favorite-btn.active {
    color: var(--error-color);
}

/* About Section */
.about-section {
    background-color: var(--surface);
    padding: 4rem 2rem;
}

.about-cards {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.about-card {
    background-color: var(--background);
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s;
}

.about-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.about-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.about-title {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.about-description {
    color: var(--text-secondary);
}

/* Contact Section */
.contact-section {
    padding: 4rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.contact-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info h3 {
    font-size: 1.75rem;
    margin-bottom: 2rem;
    color: var(--text-primary);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
}

.contact-item i {
    font-size: 1.25rem;
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background-color: var(--surface);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.3s;
}

.social-link:hover {
    background-color: var(--primary-color);
    color: white;
}

.contact-form {
    background-color: var(--surface);
    padding: 2rem;
    border-radius: 12px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--background);
    color: var(--text-primary);
    font-family: inherit;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
footer {
    background-color: var(--surface);
    padding: 3rem 2rem 1rem;
    border-top: 1px solid var(--border-color);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-column h4 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.footer-column p,
.footer-column a {
    color: var(--text-secondary);
    text-decoration: none;
    line-height: 2;
    transition: color 0.3s;
}

.footer-column a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    width: 400px;
    height: 100%;
    background-color: var(--background);
    box-shadow: var(--shadow-lg);
    z-index: 2000;
    transition: transform 0.3s;
    overflow-y: auto;
}

.sidebar-left {
    left: 0;
    transform: translateX(-100%);
}

.sidebar-right {
    right: 0;
    transform: translateX(100%);
}

.sidebar.active {
    transform: translateX(0);
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-title {
    font-size: 1.5rem;
    font-weight: 600;
}

.close-sidebar {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-primary);
}

.sidebar-content {
    padding: 1.5rem;
}

.cart-item,
.favorite-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background-color: var(--surface);
    border-radius: 8px;
    margin-bottom: 1rem;
}

.item-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    background-color: var(--border-color);
}

.item-details {
    flex: 1;
}

.item-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.item-price {
    color: var(--primary-color);
    font-weight: 500;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 0.5rem;
}

.quantity-btn {
    background-color: var(--border-color);
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
}

.quantity-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.quantity {
    font-weight: 500;
}

.remove-item {
    background: none;
    border: none;
    color: var(--error-color);
    cursor: pointer;
    font-size: 1.25rem;
}

.sidebar-footer {
    position: sticky;
    bottom: 0;
    background-color: var(--background);
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.empty-message {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 3000;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: var(--background);
    padding: 2rem;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalFadeIn 0.3s;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-primary);
}

.success-modal {
    text-align: center;
    padding: 3rem;
}

.success-icon {
    font-size: 4rem;
    color: var(--success-color);
    margin-bottom: 1rem;
}

.success-message {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.cash-on-delivery {
    background-color: var(--surface);
    padding: 1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 1.5rem 0;
    color: var(--text-secondary);
}

.cash-on-delivery i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

/* Product Details Modal */
.product-details-modal .modal-content {
    max-width: 800px;
}

.product-details-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.product-details-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 8px;
    background-color: var(--border-color);
}

.product-details-info h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.product-details-price {
    font-size: 2rem;
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 1rem;
}

.product-description {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.product-options {
    margin-bottom: 2rem;
}

.product-options h4 {
    margin-bottom: 1rem;
}

.option-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.option-btn {
    padding: 0.5rem 1.5rem;
    border: 2px solid var(--border-color);
    background-color: var(--background);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    font-family: inherit;
}

.option-btn:hover {
    border-color: var(--primary-color);
}

.option-btn.selected {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Info Pages */
.info-modal .modal-content {
    max-width: 700px;
}

.info-content {
    line-height: 1.8;
}

.info-content h3 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.info-content p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.info-content ul {
    margin-right: 2rem;
    margin-bottom: 1rem;
}

.info-content ul li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.faq-item {
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background-color: var(--surface);
    border-radius: 8px;
}

.faq-question {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.faq-answer {
    color: var(--text-secondary);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Mobile Menu */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-primary);
    cursor: pointer;
}

/* Mobile Styles */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: block;
    }

    .nav-links {
        position: fixed;
        top: 70px;
        left: 0;
        right: 0;
        background-color: var(--surface);
        flex-direction: column;
        padding: 1rem;
        transform: translateY(-100%);
        transition: transform 0.3s;
        border-bottom: 1px solid var(--border-color);
        display: none;
    }

    .nav-links.active {
        transform: translateY(0);
        display: flex;
    }

    .header-container {
        padding: 1rem;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
    }

    .contact-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .sidebar {
        width: 100%;
    }

    .product-details-container {
        grid-template-columns: 1fr;
    }

    .product-details-image {
        height: 300px;
    }
}
