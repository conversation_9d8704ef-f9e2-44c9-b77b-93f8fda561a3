# 🎨 دليل تخصيص متجر VelaSweets

## 📝 خطوات التخصيص السريع

### 1️⃣ إضافة المنتجات
افتح ملف `js/script.js` وعدل قسم المنتجات:

```javascript
const products = [
    {
        id: 1,
        name: 'اسم المنتج الأول',
        price: 15000, // السعر بالدينار
        image: 'images/product1.jpg', // ضع الصورة في مجلد images
        description: 'وصف مفصل للمنتج',
        options: ['نكهة الفانيليا', 'نكهة الشوكولاتة', 'نكهة الفراولة']
    },
    {
        id: 2,
        name: 'اسم المنتج الثاني',
        price: 12000,
        image: 'images/product2.jpg',
        description: 'وصف المنتج الثاني',
        options: ['حجم صغير', 'حجم متوسط', 'حجم كبير']
    }
    // أضف المزيد من المنتجات...
];
```

### 2️⃣ تحديث معلومات الاتصال
في ملف `index.html` ابحث عن:

**في قسم التواصل:**
```html
<span>رقم الهاتف</span> <!-- غير هذا إلى رقمك -->
<span>البريد الإلكتروني</span> <!-- غير هذا إلى إيميلك -->
<span>العنوان</span> <!-- غير هذا إلى عنوانك -->
```

**في الفوتر:**
```html
<p><i class="ri-phone-line"></i> رقم الهاتف</p>
<p><i class="ri-mail-line"></i> البريد الإلكتروني</p>
<p><i class="ri-map-pin-line"></i> العنوان</p>
```

### 3️⃣ تخصيص الألوان
في ملف `index.html` في قسم CSS، غير المتغيرات:

```css
:root {
    --primary-color: #D946EF;    /* اللون الأساسي */
    --secondary-color: #F97316;  /* اللون الثانوي */
    --accent-color: #EAB308;     /* لون التمييز */
}
```

### 4️⃣ تحديث اسم المتجر ووصفه
ابحث عن:
```html
<h1>VelaSweets</h1>
<p>حلويات فاخرة بلمسة عصرية</p>
```

### 5️⃣ إضافة الصور
1. ضع صور المنتجات في مجلد `images/`
2. تأكد من أن أسماء الصور تطابق ما في `js/script.js`
3. الأحجام المفضلة: 400x400 بكسل أو أكبر

### 6️⃣ تحديث روابط وسائل التواصل
ابحث عن:
```html
<a href="#" class="social-link"><i class="ri-facebook-fill"></i></a>
<a href="#" class="social-link"><i class="ri-instagram-line"></i></a>
<a href="#" class="social-link"><i class="ri-tiktok-line"></i></a>
```
غير `#` إلى روابط صفحاتك الحقيقية.

## 🎨 تخصيصات متقدمة

### تغيير الخط
أضف خط جديد في `<head>`:
```html
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;700&display=swap" rel="stylesheet">
```
ثم غير في CSS:
```css
body {
    font-family: 'Cairo', sans-serif;
}
```

### إضافة قسم جديد
انسخ أي قسم موجود وعدل محتواه:
```html
<section class="new-section">
    <div class="container">
        <h2 class="section-title">عنوان القسم الجديد</h2>
        <!-- محتوى القسم -->
    </div>
</section>
```

### تخصيص الرسوم المتحركة
غير مدة الحركة:
```css
.product-card {
    transition: all 0.4s ease; /* غير 0.4s إلى المدة المطلوبة */
}
```

## 🚀 نشر الموقع

### على Cloudflare Pages:
1. ارفع الملفات على GitHub
2. اربط المستودع بـ Cloudflare Pages
3. سيتم النشر تلقائياً

### على Netlify:
1. اسحب مجلد المشروع إلى Netlify
2. أو اربطه بـ GitHub

## 🔧 نصائح مهمة

### الأداء:
- ضغط الصور قبل رفعها
- استخدم تنسيق WebP للصور
- تجنب الصور الكبيرة جداً

### الأمان:
- لا تضع معلومات حساسة في الكود
- استخدم HTTPS دائماً
- احم معلومات العملاء

### التوافق:
- اختبر الموقع على أجهزة مختلفة
- تأكد من عمل الموقع على جميع المتصفحات
- اختبر سرعة التحميل

## 📱 تحسين الموبايل

الموقع متجاوب تلقائياً، لكن يمكنك تحسينه أكثر:

```css
@media (max-width: 768px) {
    .hero h1 {
        font-size: 2.5rem; /* حجم أصغر للموبايل */
    }
    
    .product-card {
        margin-bottom: 1rem;
    }
}
```

## 🎯 تحسين محركات البحث (SEO)

أضف في `<head>`:
```html
<meta name="description" content="وصف متجرك هنا">
<meta name="keywords" content="حلويات, كيك, متجر">
<meta property="og:title" content="اسم متجرك">
<meta property="og:description" content="وصف متجرك">
<meta property="og:image" content="صورة متجرك">
```

---

**بالتوفيق في تخصيص متجرك! 🎉**

إذا احتجت مساعدة إضافية، راجع الملفات أو اطلب المساعدة.
