<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VelaSweets - حلويات فاخرة</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.0.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        /* تصميم VelaSweets المحسن */
        :root {
            --primary-color: #D946EF;
            --secondary-color: #F97316;
            --accent-color: #EAB308;
            --background: #FEFEFE;
            --surface: #FFFFFF;
            --text-primary: #1F2937;
            --text-secondary: #6B7280;
            --border-color: #E5E7EB;
            --success-color: #10B981;
            --error-color: #EF4444;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --gradient-primary: linear-gradient(135deg, #D946EF 0%, #F97316 100%);
            --gradient-secondary: linear-gradient(135deg, #F97316 0%, #EAB308 100%);
        }

        [data-theme="dark"] {
            --background: #0F172A;
            --surface: #1E293B;
            --text-primary: #F1F5F9;
            --text-secondary: #94A3B8;
            --border-color: #334155;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background: var(--background);
            color: var(--text-primary);
            line-height: 1.7;
            transition: all 0.3s ease;
            overflow-x: hidden;
        }

        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(229, 231, 235, 0.3);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        header:hover {
            box-shadow: var(--shadow-lg);
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-links a:hover {
            color: var(--primary-color);
        }

        .header-icons {
            display: flex;
            gap: 1.5rem;
            align-items: center;
        }

        .icon-btn {
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 1.25rem;
            cursor: pointer;
            position: relative;
            transition: color 0.3s;
        }

        .icon-btn:hover {
            color: var(--primary-color);
        }

        .badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--accent-color);
            color: white;
            font-size: 0.75rem;
            padding: 2px 6px;
            border-radius: 9999px;
            font-weight: bold;
        }

        .hero {
            background: var(--gradient-primary);
            color: white;
            padding: 10rem 2rem 6rem;
            text-align: center;
            margin-top: 70px;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero h1 {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            animation: fadeInUp 0.8s ease-out;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1;
        }

        .hero p {
            font-size: 1.5rem;
            margin-bottom: 3rem;
            opacity: 0.95;
            animation: fadeInUp 0.8s 0.2s both;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            z-index: 1;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            animation: fadeInUp 0.8s 0.4s both;
        }

        .btn {
            padding: 1rem 2.5rem;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
            font-family: inherit;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: white;
            color: var(--primary-color);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            background: #f8fafc;
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: white;
            color: var(--primary-color);
            border-color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
        }

        .products-section {
            padding: 4rem 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: var(--text-primary);
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 2rem;
        }

        .product-card {
            background: var(--surface);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            border: 1px solid rgba(229, 231, 235, 0.5);
            position: relative;
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 20px;
        }

        .product-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(217, 70, 239, 0.2);
        }

        .product-card:hover::before {
            opacity: 0.05;
        }

        .product-image {
            width: 100%;
            height: 280px;
            object-fit: cover;
            background: linear-gradient(45deg, #f3f4f6, #e5e7eb);
            transition: transform 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .product-card:hover .product-image {
            transform: scale(1.05);
        }

        .product-info {
            padding: 2rem;
            position: relative;
            z-index: 1;
        }

        .product-name {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: var(--text-primary);
            line-height: 1.3;
        }

        .product-price {
            background: var(--gradient-primary);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 1.75rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            display: block;
        }

        .product-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .add-to-cart {
            flex: 1;
            background: var(--gradient-primary);
            color: white;
            padding: 1rem 1.5rem;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            position: relative;
            overflow: hidden;
        }

        .add-to-cart::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .add-to-cart:hover::before {
            left: 100%;
        }

        .add-to-cart:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(217, 70, 239, 0.4);
        }

        .favorite-btn {
            background: rgba(107, 114, 128, 0.1);
            border: none;
            font-size: 1.5rem;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .favorite-btn:hover {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
            transform: scale(1.1);
        }

        .favorite-btn.active {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* تحسينات إضافية */
        .section-title {
            position: relative;
            display: inline-block;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: var(--gradient-primary);
            border-radius: 2px;
        }

        /* تحسين الأيقونات */
        .icon-btn {
            position: relative;
            overflow: hidden;
        }

        .icon-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(217, 70, 239, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
        }

        .icon-btn:hover::before {
            width: 40px;
            height: 40px;
        }

        /* تحسين الروابط */
        .nav-links a {
            position: relative;
            overflow: hidden;
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--gradient-primary);
            transition: width 0.3s ease;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .hero-buttons {
                flex-direction: column;
                width: 100%;
                max-width: 300px;
                margin: 0 auto;
            }

            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-container">
            <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
                <i class="ri-menu-line"></i>
            </button>
            <nav>
                <ul class="nav-links" id="navLinks">
                    <li><a href="#home">الرئيسية</a></li>
                    <li><a href="#products">المنتجات</a></li>
                    <li><a href="#" onclick="showInfoModal('faq')">مركز المعلومات</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <button class="icon-btn" onclick="toggleTheme()">
                    <i class="ri-moon-line" id="themeIcon"></i>
                </button>
                <button class="icon-btn" onclick="toggleFavorites()">
                    <i class="ri-heart-line"></i>
                    <span class="badge" id="favoritesCount">0</span>
                </button>
                <button class="icon-btn" onclick="toggleCart()">
                    <i class="ri-shopping-cart-line"></i>
                    <span class="badge" id="cartCount">0</span>
                </button>
                <button class="icon-btn">
                    <i class="ri-user-line"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <h1>VelaSweets</h1>
        <p>حلويات فاخرة بلمسة عصرية</p>
        <div class="hero-buttons">
            <a href="#products" class="btn btn-primary">شاهد المنتجات</a>
            <a href="#about" class="btn btn-secondary">من نحن</a>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products-section" id="products">
        <h2 class="section-title">منتجاتنا</h2>
        <div class="products-grid" id="productsGrid">
            <!-- Products will be loaded here -->
        </div>
    </section>

    <!-- About Section -->
    <section class="about-section" id="about">
        <h2 class="section-title">من نحن</h2>
        <div class="about-cards">
            <div class="about-card">
                <div class="about-icon">🏆</div>
                <h3 class="about-title">جودة عالية</h3>
                <p class="about-description">نستخدم أجود المكونات الطبيعية لضمان طعم لا يُنسى</p>
            </div>
            <div class="about-card">
                <div class="about-icon">🚚</div>
                <h3 class="about-title">توصيل سريع</h3>
                <p class="about-description">نوصل طلباتك بسرعة للحفاظ على طزاجة المنتجات</p>
            </div>
            <div class="about-card">
                <div class="about-icon">📞</div>
                <h3 class="about-title">خدمة ممتازة</h3>
                <p class="about-description">فريق دعم متخصص لخدمتك على مدار الساعة</p>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section" id="contact">
        <h2 class="section-title">تواصل معنا</h2>
        <div class="contact-container">
            <div class="contact-info">
                <h3>معلومات الاتصال</h3>
                <div class="contact-item">
                    <i class="ri-phone-line"></i>
                    <span>رقم الهاتف</span>
                </div>
                <div class="contact-item">
                    <i class="ri-mail-line"></i>
                    <span>البريد الإلكتروني</span>
                </div>
                <div class="contact-item">
                    <i class="ri-map-pin-line"></i>
                    <span>العنوان</span>
                </div>
                <div class="social-links">
                    <a href="#" class="social-link">
                        <i class="ri-facebook-fill"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="ri-instagram-line"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="ri-tiktok-fill"></i>
                    </a>
                </div>
            </div>
            <form class="contact-form" onsubmit="handleContactSubmit(event)">
                <div class="form-group">
                    <label for="name">الاسم</label>
                    <input type="text" id="name" required>
                </div>
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" required>
                </div>
                <div class="form-group">
                    <label for="message">الرسالة</label>
                    <textarea id="message" required></textarea>
                </div>
                <button type="submit" class="btn btn-primary" style="width: 100%;">إرسال</button>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-column">
                <h4>VelaSweets</h4>
                <p>حلويات فاخرة بلمسة عصرية</p>
                <div class="social-links">
                    <a href="#" class="social-link">
                        <i class="ri-facebook-fill"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="ri-instagram-line"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="ri-tiktok-fill"></i>
                    </a>
                </div>
            </div>
            <div class="footer-column">
                <h4>روابط سريعة</h4>
                <a href="#home">الرئيسية</a><br>
                <a href="#products">المنتجات</a><br>
                <a href="#about">من نحن</a><br>
                <a href="#contact">تواصل معنا</a><br>
                <a href="#" onclick="showInfoModal('privacy')">سياسة الخصوصية</a><br>
                <a href="#" onclick="showInfoModal('terms')">الشروط والأحكام</a>
            </div>
            <div class="footer-column">
                <h4>معلومات الاتصال</h4>
                <p><i class="ri-phone-line"></i> رقم الهاتف</p>
                <p><i class="ri-mail-line"></i> البريد الإلكتروني</p>
                <p><i class="ri-map-pin-line"></i> العنوان</p>
            </div>
        </div>
        <div class="footer-bottom">
            <p>جميع الحقوق محفوظة © 2026 VelaSweets</p>
        </div>
    </footer>

    <!-- Cart Sidebar -->
    <div class="sidebar sidebar-left" id="cartSidebar">
        <div class="sidebar-header">
            <h3 class="sidebar-title">السلة</h3>
            <button class="close-sidebar" onclick="toggleCart()">
                <i class="ri-close-line"></i>
            </button>
        </div>
        <div class="sidebar-content" id="cartContent">
            <!-- Cart items will be loaded here -->
        </div>
        <div class="sidebar-footer" id="cartFooter" style="display: none;">
            <div class="total">
                <span>المجموع:</span>
                <span id="cartTotal">0 دينار</span>
            </div>
            <button class="btn btn-primary" style="width: 100%;" onclick="showOrderModal()">
                اطلب الآن
            </button>
        </div>
    </div>

    <!-- Favorites Sidebar -->
    <div class="sidebar sidebar-right" id="favoritesSidebar">
        <div class="sidebar-header">
            <h3 class="sidebar-title">المفضلة</h3>
            <button class="close-sidebar" onclick="toggleFavorites()">
                <i class="ri-close-line"></i>
            </button>
        </div>
        <div class="sidebar-content" id="favoritesContent">
            <!-- Favorite items will be loaded here -->
        </div>
        <div class="sidebar-footer" id="favoritesFooter" style="display: none;">
            <button class="btn btn-secondary" style="width: 100%;" onclick="clearFavorites()">
                مسح الكل
            </button>
        </div>
    </div>

    <!-- Order Modal -->
    <div class="modal" id="orderModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">إتمام الطلب</h3>
                <button class="close-modal" onclick="closeModal('orderModal')">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <form onsubmit="handleOrderSubmit(event)">
                <div class="form-group">
                    <label for="fullName">الاسم الكامل</label>
                    <input type="text" id="fullName" required>
                </div>
                <div class="form-group">
                    <label for="phone">رقم الهاتف</label>
                    <input type="tel" id="phone" required>
                </div>
                <div class="form-group">
                    <label for="province">المحافظة</label>
                    <select id="province" required>
                        <option value="">اختر المحافظة</option>
                        <option value="البصرة">البصرة</option>
                        <option value="بغداد">بغداد</option>
                        <option value="الموصل">الموصل</option>
                        <option value="النجف">النجف</option>
                        <option value="كربلاء">كربلاء</option>
                        <option value="بابل">بابل</option>
                        <option value="ديالى">ديالى</option>
                        <option value="ذي قار">ذي قار</option>
                        <option value="ميسان">ميسان</option>
                        <option value="واسط">واسط</option>
                        <option value="المثنى">المثنى</option>
                        <option value="القادسية">القادسية</option>
                        <option value="الأنبار">الأنبار</option>
                        <option value="صلاح الدين">صلاح الدين</option>
                        <option value="كركوك">كركوك</option>
                        <option value="السليمانية">السليمانية</option>
                        <option value="أربيل">أربيل</option>
                        <option value="دهوك">دهوك</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="address">العنوان التفصيلي</label>
                    <textarea id="address" required></textarea>
                </div>
                <div class="cash-on-delivery">
                    <i class="ri-money-dollar-circle-line"></i>
                    <span>الدفع عند الاستلام</span>
                </div>
                <button type="submit" class="btn btn-primary" style="width: 100%;">
                    تأكيد الطلب
                </button>
            </form>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal" id="successModal">
        <div class="modal-content success-modal">
            <div class="success-icon">✓</div>
            <h3 class="success-message">تم الطلب بنجاح!</h3>
            <p>شكراً لك، سيتم التواصل معك قريباً لتأكيد الطلب</p>
            <button class="btn btn-primary" onclick="closeSuccessModal()">
                العودة للمتجر
            </button>
        </div>
    </div>

    <!-- Product Details Modal -->
    <div class="modal product-details-modal" id="productDetailsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">تفاصيل المنتج</h3>
                <button class="close-modal" onclick="closeModal('productDetailsModal')">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <div id="productDetailsContent">
                <!-- Product details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Info Modal -->
    <div class="modal info-modal" id="infoModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="infoModalTitle"></h3>
                <button class="close-modal" onclick="closeModal('infoModal')">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <div class="info-content" id="infoModalContent">
                <!-- Info content will be loaded here -->
            </div>
        </div>
    </div>

    <script src="js/script.js"></script>
</body>
</html>