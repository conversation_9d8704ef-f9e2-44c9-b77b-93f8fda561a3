<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VelaSweets - حلويات فاخرة</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.0.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-container">
            <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
                <i class="ri-menu-line"></i>
            </button>
            <nav>
                <ul class="nav-links" id="navLinks">
                    <li><a href="#home">الرئيسية</a></li>
                    <li><a href="#products">المنتجات</a></li>
                    <li><a href="#" onclick="showInfoModal('faq')">مركز المعلومات</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <button class="icon-btn" onclick="toggleTheme()">
                    <i class="ri-moon-line" id="themeIcon"></i>
                </button>
                <button class="icon-btn" onclick="toggleFavorites()">
                    <i class="ri-heart-line"></i>
                    <span class="badge" id="favoritesCount">0</span>
                </button>
                <button class="icon-btn" onclick="toggleCart()">
                    <i class="ri-shopping-cart-line"></i>
                    <span class="badge" id="cartCount">0</span>
                </button>
                <button class="icon-btn">
                    <i class="ri-user-line"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <h1>VelaSweets</h1>
        <p>حلويات فاخرة بلمسة عصرية</p>
        <div class="hero-buttons">
            <a href="#products" class="btn btn-primary">شاهد المنتجات</a>
            <a href="#about" class="btn btn-secondary">من نحن</a>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products-section" id="products">
        <h2 class="section-title">منتجاتنا</h2>
        <div class="products-grid" id="productsGrid">
            <!-- Products will be loaded here -->
        </div>
    </section>

    <!-- About Section -->
    <section class="about-section" id="about">
        <h2 class="section-title">من نحن</h2>
        <div class="about-cards">
            <div class="about-card">
                <div class="about-icon">🏆</div>
                <h3 class="about-title">جودة عالية</h3>
                <p class="about-description">نستخدم أجود المكونات الطبيعية لضمان طعم لا يُنسى</p>
            </div>
            <div class="about-card">
                <div class="about-icon">🚚</div>
                <h3 class="about-title">توصيل سريع</h3>
                <p class="about-description">نوصل طلباتك بسرعة للحفاظ على طزاجة المنتجات</p>
            </div>
            <div class="about-card">
                <div class="about-icon">📞</div>
                <h3 class="about-title">خدمة ممتازة</h3>
                <p class="about-description">فريق دعم متخصص لخدمتك على مدار الساعة</p>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section" id="contact">
        <h2 class="section-title">تواصل معنا</h2>
        <div class="contact-container">
            <div class="contact-info">
                <h3>معلومات الاتصال</h3>
                <div class="contact-item">
                    <i class="ri-phone-line"></i>
                    <span>07800000000</span>
                </div>
                <div class="contact-item">
                    <i class="ri-mail-line"></i>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <i class="ri-map-pin-line"></i>
                    <span>البصرة - العراق</span>
                </div>
                <div class="social-links">
                    <a href="#" class="social-link">
                        <i class="ri-facebook-fill"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="ri-instagram-line"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="ri-tiktok-fill"></i>
                    </a>
                </div>
            </div>
            <form class="contact-form" onsubmit="handleContactSubmit(event)">
                <div class="form-group">
                    <label for="name">الاسم</label>
                    <input type="text" id="name" required>
                </div>
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" required>
                </div>
                <div class="form-group">
                    <label for="message">الرسالة</label>
                    <textarea id="message" required></textarea>
                </div>
                <button type="submit" class="btn btn-primary" style="width: 100%;">إرسال</button>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-column">
                <h4>VelaSweets</h4>
                <p>حلويات فاخرة بلمسة عصرية</p>
                <div class="social-links">
                    <a href="#" class="social-link">
                        <i class="ri-facebook-fill"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="ri-instagram-line"></i>
                    </a>
                    <a href="#" class="social-link">
                        <i class="ri-tiktok-fill"></i>
                    </a>
                </div>
            </div>
            <div class="footer-column">
                <h4>روابط سريعة</h4>
                <a href="#home">الرئيسية</a><br>
                <a href="#products">المنتجات</a><br>
                <a href="#about">من نحن</a><br>
                <a href="#contact">تواصل معنا</a><br>
                <a href="#" onclick="showInfoModal('privacy')">سياسة الخصوصية</a><br>
                <a href="#" onclick="showInfoModal('terms')">الشروط والأحكام</a>
            </div>
            <div class="footer-column">
                <h4>معلومات الاتصال</h4>
                <p><i class="ri-phone-line"></i> 07800000000</p>
                <p><i class="ri-mail-line"></i> <EMAIL></p>
                <p><i class="ri-map-pin-line"></i> البصرة - العراق</p>
            </div>
        </div>
        <div class="footer-bottom">
            <p>جميع الحقوق محفوظة © 2026 VelaSweets</p>
        </div>
    </footer>

    <!-- Cart Sidebar -->
    <div class="sidebar sidebar-left" id="cartSidebar">
        <div class="sidebar-header">
            <h3 class="sidebar-title">السلة</h3>
            <button class="close-sidebar" onclick="toggleCart()">
                <i class="ri-close-line"></i>
            </button>
        </div>
        <div class="sidebar-content" id="cartContent">
            <!-- Cart items will be loaded here -->
        </div>
        <div class="sidebar-footer" id="cartFooter" style="display: none;">
            <div class="total">
                <span>المجموع:</span>
                <span id="cartTotal">0 دينار</span>
            </div>
            <button class="btn btn-primary" style="width: 100%;" onclick="showOrderModal()">
                اطلب الآن
            </button>
        </div>
    </div>

    <!-- Favorites Sidebar -->
    <div class="sidebar sidebar-right" id="favoritesSidebar">
        <div class="sidebar-header">
            <h3 class="sidebar-title">المفضلة</h3>
            <button class="close-sidebar" onclick="toggleFavorites()">
                <i class="ri-close-line"></i>
            </button>
        </div>
        <div class="sidebar-content" id="favoritesContent">
            <!-- Favorite items will be loaded here -->
        </div>
        <div class="sidebar-footer" id="favoritesFooter" style="display: none;">
            <button class="btn btn-secondary" style="width: 100%;" onclick="clearFavorites()">
                مسح الكل
            </button>
        </div>
    </div>

    <!-- Order Modal -->
    <div class="modal" id="orderModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">إتمام الطلب</h3>
                <button class="close-modal" onclick="closeModal('orderModal')">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <form onsubmit="handleOrderSubmit(event)">
                <div class="form-group">
                    <label for="fullName">الاسم الكامل</label>
                    <input type="text" id="fullName" required>
                </div>
                <div class="form-group">
                    <label for="phone">رقم الهاتف</label>
                    <input type="tel" id="phone" required>
                </div>
                <div class="form-group">
                    <label for="province">المحافظة</label>
                    <select id="province" required>
                        <option value="">اختر المحافظة</option>
                        <option value="البصرة">البصرة</option>
                        <option value="بغداد">بغداد</option>
                        <option value="الموصل">الموصل</option>
                        <option value="النجف">النجف</option>
                        <option value="كربلاء">كربلاء</option>
                        <option value="بابل">بابل</option>
                        <option value="ديالى">ديالى</option>
                        <option value="ذي قار">ذي قار</option>
                        <option value="ميسان">ميسان</option>
                        <option value="واسط">واسط</option>
                        <option value="المثنى">المثنى</option>
                        <option value="القادسية">القادسية</option>
                        <option value="الأنبار">الأنبار</option>
                        <option value="صلاح الدين">صلاح الدين</option>
                        <option value="كركوك">كركوك</option>
                        <option value="السليمانية">السليمانية</option>
                        <option value="أربيل">أربيل</option>
                        <option value="دهوك">دهوك</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="address">العنوان التفصيلي</label>
                    <textarea id="address" required></textarea>
                </div>
                <div class="cash-on-delivery">
                    <i class="ri-money-dollar-circle-line"></i>
                    <span>الدفع عند الاستلام</span>
                </div>
                <button type="submit" class="btn btn-primary" style="width: 100%;">
                    تأكيد الطلب
                </button>
            </form>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal" id="successModal">
        <div class="modal-content success-modal">
            <div class="success-icon">✓</div>
            <h3 class="success-message">تم الطلب بنجاح!</h3>
            <p>شكراً لك، سيتم التواصل معك قريباً لتأكيد الطلب</p>
            <button class="btn btn-primary" onclick="closeSuccessModal()">
                العودة للمتجر
            </button>
        </div>
    </div>

    <!-- Product Details Modal -->
    <div class="modal product-details-modal" id="productDetailsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">تفاصيل المنتج</h3>
                <button class="close-modal" onclick="closeModal('productDetailsModal')">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <div id="productDetailsContent">
                <!-- Product details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Info Modal -->
    <div class="modal info-modal" id="infoModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="infoModalTitle"></h3>
                <button class="close-modal" onclick="closeModal('infoModal')">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <div class="info-content" id="infoModalContent">
                <!-- Info content will be loaded here -->
            </div>
        </div>
    </div>

    <script src="js/script.js"></script>
</body>
</html>