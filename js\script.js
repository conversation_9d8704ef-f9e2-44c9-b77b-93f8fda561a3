// Products Data
const products = [
    {
        id: 1,
        name: 'جوزية',
        price: 7000,
        image: 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="280" height="250" viewBox="0 0 280 250"%3E%3Crect width="280" height="250" fill="%23E5E7EB"/%3E%3Ctext x="50%25" y="50%25" text-anchor="middle" dy=".3em" fill="%236B7280" font-family="Arial" font-size="16"%3Eجوزية%3C/text%3E%3C/svg%3E',
        description: 'قطع جوزية محشية ومغطاة بطبقة شوكولاتة فاخرة',
        options: ['الكراميل', 'الشوكولاتة', 'البستاشيو']
    },
    {
        id: 2,
        name: 'ماد<PERSON>ين كيك',
        price: 4500,
        image: 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="280" height="250" viewBox="0 0 280 250"%3E%3Crect width="280" height="250" fill="%23E5E7EB"/%3E%3Ctext x="50%25" y="50%25" text-anchor="middle" dy=".3em" fill="%236B7280" font-family="Arial" font-size="16"%3Eمادلين كيك%3C/text%3E%3C/svg%3E',
        description: 'كيك ناعم ومميز مغطى بشوكولاتة فاخرة ومزين بالبندق المحمص',
        options: ['بالبندق']
    },
    {
        id: 3,
        name: 'حلى ڤيلا',
        price: 6000,
        image: 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="280" height="250" viewBox="0 0 280 250"%3E%3Crect width="280" height="250" fill="%23E5E7EB"/%3E%3Ctext x="50%25" y="50%25" text-anchor="middle" dy=".3em" fill="%236B7280" font-family="Arial" font-size="16"%3Eحلى ڤيلا%3C/text%3E%3C/svg%3E',
        description: 'حلى فاخر بطبقات متعددة من الكريمة والفول السوداني المقرمش',
        options: ['بالفول السوداني']
    }
];

// State Management
let cart = [];
let favorites = [];
let selectedOptions = {};

// Theme Toggle
function toggleTheme() {
    const currentTheme = document.body.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    document.body.setAttribute('data-theme', newTheme);
    document.getElementById('themeIcon').className = newTheme === 'dark' ? 'ri-sun-line' : 'ri-moon-line';
    localStorage.setItem('theme', newTheme);
}

// Mobile Menu Toggle
function toggleMobileMenu() {
    const navLinks = document.getElementById('navLinks');
    navLinks.classList.toggle('active');
}

// Initialize
document.addEventListener('DOMContentLoaded', () => {
    loadFromLocalStorage();
    renderProducts();
    updateCartUI();
    updateFavoritesUI();
    
    // Check theme
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.setAttribute('data-theme', 'dark');
        document.getElementById('themeIcon').className = 'ri-sun-line';
    }
});

// Render Products
function renderProducts() {
    const productsGrid = document.getElementById('productsGrid');
    productsGrid.innerHTML = products.map(product => `
        <div class="product-card" onclick="showProductDetails(${product.id})">
            <img src="${product.image}" alt="${product.name}" class="product-image">
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <div class="product-price">${product.price.toLocaleString()} دينار</div>
                <div class="product-actions">
                    <button class="add-to-cart" onclick="event.stopPropagation(); addToCart(${product.id})">
                        أضف للسلة
                    </button>
                    <button class="favorite-btn ${favorites.includes(product.id) ? 'active' : ''}" 
                            onclick="event.stopPropagation(); toggleFavorite(${product.id})">
                        <i class="ri-heart-${favorites.includes(product.id) ? 'fill' : 'line'}"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// Show Product Details
function showProductDetails(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    selectedOptions[productId] = selectedOptions[productId] || product.options[0];

    const content = `
        <div class="product-details-container">
            <img src="${product.image}" alt="${product.name}" class="product-details-image">
            <div class="product-details-info">
                <h2>${product.name}</h2>
                <div class="product-details-price">${product.price.toLocaleString()} دينار</div>
                <p class="product-description">${product.description}</p>
                <div class="product-options">
                    <h4>خيارات النكهة:</h4>
                    <div class="option-buttons">
                        ${product.options.map(option => `
                            <button class="option-btn ${selectedOptions[productId] === option ? 'selected' : ''}"
                                    onclick="selectOption(${productId}, '${option}')">
                                ${option}
                            </button>
                        `).join('')}
                    </div>
                </div>
                <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                    <button class="btn btn-primary" style="flex: 1;" onclick="addToCart(${productId}); closeModal('productDetailsModal');">
                        أضف للسلة
                    </button>
                    <button class="favorite-btn ${favorites.includes(productId) ? 'active' : ''}" 
                            style="font-size: 2rem;" onclick="toggleFavorite(${productId})">
                        <i class="ri-heart-${favorites.includes(productId) ? 'fill' : 'line'}"></i>
                    </button>
                </div>
            </div>
        </div>
    `;

    document.getElementById('productDetailsContent').innerHTML = content;
    document.getElementById('productDetailsModal').classList.add('active');
}

// Select Option
function selectOption(productId, option) {
    selectedOptions[productId] = option;
    showProductDetails(productId); // Re-render to update selection
}

// Cart Functions
function addToCart(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    const existingItem = cart.find(item => item.id === productId);
    if (existingItem) {
        existingItem.quantity++;
    } else {
        cart.push({
            ...product,
            quantity: 1,
            selectedOption: selectedOptions[productId] || product.options[0]
        });
    }

    saveToLocalStorage();
    updateCartUI();
    showNotification('تمت الإضافة إلى السلة');
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    saveToLocalStorage();
    updateCartUI();
}

function updateQuantity(productId, change) {
    const item = cart.find(item => item.id === productId);
    if (item) {
        item.quantity += change;
        if (item.quantity <= 0) {
            removeFromCart(productId);
        } else {
            saveToLocalStorage();
            updateCartUI();
        }
    }
}

function updateCartUI() {
    const cartContent = document.getElementById('cartContent');
    const cartFooter = document.getElementById('cartFooter');
    const cartCount = document.getElementById('cartCount');

    if (cart.length === 0) {
        cartContent.innerHTML = `
            <div class="empty-message">
                <div class="empty-icon">🛒</div>
                <p>السلة فارغة</p>
            </div>
        `;
        cartFooter.style.display = 'none';
    } else {
        cartContent.innerHTML = cart.map(item => `
            <div class="cart-item">
                <img src="${item.image}" alt="${item.name}" class="item-image">
                <div class="item-details">
                    <div class="item-name">${item.name}</div>
                    <div class="item-price">${item.price.toLocaleString()} دينار</div>
                    <div style="font-size: 0.875rem; color: var(--text-secondary);">${item.selectedOption}</div>
                    <div class="quantity-controls">
                        <button class="quantity-btn" onclick="updateQuantity(${item.id}, -1)">-</button>
                        <span class="quantity">${item.quantity}</span>
                        <button class="quantity-btn" onclick="updateQuantity(${item.id}, 1)">+</button>
                    </div>
                </div>
                <button class="remove-item" onclick="removeFromCart(${item.id})">
                    <i class="ri-delete-bin-line"></i>
                </button>
            </div>
        `).join('');
        cartFooter.style.display = 'block';
    }

    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    document.getElementById('cartTotal').textContent = `${total.toLocaleString()} دينار`;
    cartCount.textContent = cart.reduce((sum, item) => sum + item.quantity, 0);
}

function toggleCart() {
    const cartSidebar = document.getElementById('cartSidebar');
    const favoritesSidebar = document.getElementById('favoritesSidebar');

    // Close favorites if open
    favoritesSidebar.classList.remove('active');

    cartSidebar.classList.toggle('active');
}

// Favorites Functions
function toggleFavorite(productId) {
    const index = favorites.indexOf(productId);
    if (index > -1) {
        favorites.splice(index, 1);
    } else {
        favorites.push(productId);
    }

    saveToLocalStorage();
    updateFavoritesUI();
    renderProducts(); // Update heart icons
}

function updateFavoritesUI() {
    const favoritesContent = document.getElementById('favoritesContent');
    const favoritesFooter = document.getElementById('favoritesFooter');
    const favoritesCount = document.getElementById('favoritesCount');

    if (favorites.length === 0) {
        favoritesContent.innerHTML = `
            <div class="empty-message">
                <div class="empty-icon">❤️</div>
                <p>لا توجد منتجات مفضلة</p>
            </div>
        `;
        favoritesFooter.style.display = 'none';
    } else {
        const favoriteProducts = products.filter(p => favorites.includes(p.id));
        favoritesContent.innerHTML = favoriteProducts.map(item => `
            <div class="favorite-item">
                <img src="${item.image}" alt="${item.name}" class="item-image">
                <div class="item-details">
                    <div class="item-name">${item.name}</div>
                    <div class="item-price">${item.price.toLocaleString()} دينار</div>
                    <button class="btn btn-primary" style="margin-top: 0.5rem;"
                            onclick="addToCart(${item.id}); toggleFavorite(${item.id});">
                        أضف للسلة
                    </button>
                </div>
                <button class="remove-item" onclick="toggleFavorite(${item.id})">
                    <i class="ri-heart-fill"></i>
                </button>
            </div>
        `).join('');
        favoritesFooter.style.display = 'block';
    }

    favoritesCount.textContent = favorites.length;
}

function toggleFavorites() {
    const favoritesSidebar = document.getElementById('favoritesSidebar');
    const cartSidebar = document.getElementById('cartSidebar');

    // Close cart if open
    cartSidebar.classList.remove('active');

    favoritesSidebar.classList.toggle('active');
}

function clearFavorites() {
    favorites = [];
    saveToLocalStorage();
    updateFavoritesUI();
    renderProducts();
}

// Modal Functions
function showOrderModal() {
    if (cart.length === 0) {
        showNotification('السلة فارغة');
        return;
    }
    document.getElementById('orderModal').classList.add('active');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('active');
}

function handleOrderSubmit(event) {
    event.preventDefault();

    const orderData = {
        fullName: document.getElementById('fullName').value,
        phone: document.getElementById('phone').value,
        province: document.getElementById('province').value,
        address: document.getElementById('address').value,
        items: cart,
        total: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        date: new Date().toISOString()
    };

    // Save order
    const orders = JSON.parse(localStorage.getItem('orders') || '[]');
    orders.push(orderData);
    localStorage.setItem('orders', JSON.stringify(orders));

    // Clear cart
    cart = [];
    saveToLocalStorage();
    updateCartUI();

    // Show success
    closeModal('orderModal');
    document.getElementById('successModal').classList.add('active');
}

function closeSuccessModal() {
    closeModal('successModal');
    toggleCart(); // Close cart sidebar
}

function handleContactSubmit(event) {
    event.preventDefault();
    showNotification('تم إرسال رسالتك بنجاح');
    event.target.reset();
}

// Info Modal
function showInfoModal(type) {
    const modal = document.getElementById('infoModal');
    const title = document.getElementById('infoModalTitle');
    const content = document.getElementById('infoModalContent');

    switch(type) {
        case 'faq':
            title.textContent = 'الأسئلة الشائعة';
            content.innerHTML = `
                <div class="faq-item">
                    <div class="faq-question">س: كيف يمكنني طلب منتجاتكم؟</div>
                    <div class="faq-answer">ج: يمكنك الطلب من خلال موقعنا الإلكتروني، بإضافة المنتجات إلى السلة ثم إتمام الطلب بعد التأكيد.</div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">س: كم تستغرق مدة التوصيل؟</div>
                    <div class="faq-answer">ج: تختلف مدة التوصيل حسب المحافظة:<br>
                    • البصرة: من نفس اليوم إلى 24 ساعة.<br>
                    • باقي المحافظات: من 2 إلى 3 أيام حسب المسافة.</div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">س: هل يمكنني إلغاء أو تعديل طلبي؟</div>
                    <div class="faq-answer">ج: نعم، يمكن الإلغاء أو التعديل خلال ساعة واحدة من تأكيد الطلب. بعد مرور الساعة يبدأ التحضير، ولا يُمكن التعديل أو الإلغاء بعد ذلك.</div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">س: ما هي طرق الدفع المتاحة؟</div>
                    <div class="faq-answer">ج: حاليًا نقبل الدفع عند الاستلام فقط. نعمل على توفير خيارات دفع إلكتروني قريبًا بإذن الله.</div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">س: هل تقدمون حلويات للمناسبات الخاصة؟</div>
                    <div class="faq-answer">ج: لا نقدم منتجات مخصصة للمناسبات حالياً، لكن يمكنك اختيار ما يناسبك من تشكيلتنا الجاهزة المناسبة للتجمعات الصغيرة.</div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">س: كم تبقى منتجاتكم طازجة؟</div>
                    <div class="faq-answer">ج: تبقى منتجاتنا طازجة لمدة 3 إلى 5 أيام إذا حُفظت في مكان بارد وجاف. للحصول على أفضل طعم، ننصح باستهلاكها خلال أول يومين.</div>
                </div>
            `;
            break;

        case 'privacy':
            title.textContent = 'سياسة الخصوصية';
            content.innerHTML = `
                <h3>سياسة الخصوصية</h3>
                <p>• نحتفظ ببياناتك الشخصية بسرية تامة.</p>
                <p>• لا نشارك معلوماتك مع أي جهة خارجية.</p>
                <p>• نستخدم البيانات لتحسين خدماتنا فقط.</p>
                <p>• يمكنك طلب حذف بياناتك في أي وقت.</p>

                <h3>تنويه</h3>
                <p>• جميع منتجاتنا مصنوعة من مكونات طبيعية.</p>
                <p>• قد تحتوي على مكسرات أو مواد مسببة للحساسية.</p>
                <p>• يرجى إبلاغنا عن أي حساسية غذائية عند الطلب.</p>
                <p>• هذه الشروط قابلة للتعديل مع إعلام الزبائن.</p>
            `;
            break;

        case 'terms':
            title.textContent = 'الشروط والأحكام';
            content.innerHTML = `
                <h3>شروط الطلب</h3>
                <p>• نبدأ بتحضير الطلب بعد تأكيد الزبون والموافقة على الشروط.</p>
                <p>• جميع الطلبات تُحضّر حسب الطلب لضمان الطزاجة والجودة.</p>
                <p>• يُفضل إرسال الطلب بوقت كافٍ لضمان توفر المنتج.</p>
                <p>• لا يمكن إلغاء أو تعديل الطلب بعد بدء التحضير إلا باتفاق الطرفين.</p>
                <p>• الطلبات تُنفذ حسب وقت استلامها.</p>

                <h3>شروط التوصيل</h3>
                <p>• تُضاف رسوم التوصيل إلى قيمة الطلب.</p>
                <p>• يجب تواجد أحد لاستلام الطلب في الوقت المحدد.</p>
                <p>• في حالة عدم التواجد، يُعاد الطلب ويُخصم فقط مبلغ رسوم التوصيل إذا تم إعلام الزبون بذلك مسبقًا.</p>
                <p>• لا نتحمل مسؤولية التأخير الناتج عن ظروف خارجة عن إرادتنا كالأحوال الجوية أو الطرق.</p>

                <h3>شروط الإرجاع والاستبدال</h3>
                <p>• لا يُقبل الإرجاع أو الاستبدال إلا في حالة وجود عيب واضح في المنتج.</p>
                <p>• يُفحص المنتج فور التسليم بحضور المندوب والزبون.</p>
                <p>• يمكن للزبون التبليغ عن العيب خلال ساعة من وقت الاستلام.</p>
                <p>• في حال ثبوت وجود عيب، يتم استرجاع المنتج مع تحمل الزبون رسوم التوصيل فقط.</p>
                <p>• لا نتحمل مسؤولية تلف المنتج بعد التسليم وقبول الزبون له.</p>

                <h3>الالتزام والتعامل</h3>
                <p>• إذا رفض الزبون استلام الطلب بدون سبب مشروع، أو امتنع عن الرد على المندوب، أو حظر صفحة التواصل، نحتفظ بحق التوقف عن التعامل معه مستقبلاً.</p>
                <p>• أي نزاع يُحال إلى الجهات القانونية المختصة للنظر فيه.</p>
            `;
            break;
    }

    document.getElementById('infoModal').classList.add('active');
}

// Utility Functions
function saveToLocalStorage() {
    localStorage.setItem('cart', JSON.stringify(cart));
    localStorage.setItem('favorites', JSON.stringify(favorites));
    localStorage.setItem('selectedOptions', JSON.stringify(selectedOptions));
}

function loadFromLocalStorage() {
    cart = JSON.parse(localStorage.getItem('cart') || '[]');
    favorites = JSON.parse(localStorage.getItem('favorites') || '[]');
    selectedOptions = JSON.parse(localStorage.getItem('selectedOptions') || '{}');
}

function showNotification(message) {
    // Simple notification - you can enhance this
    alert(message);
}
